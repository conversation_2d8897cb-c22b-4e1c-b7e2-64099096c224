%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!1 &171016
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 100100000}
  serializedVersion: 4
  m_Component:
  - 4: {fileID: 494800}
  - 33: {fileID: 3354176}
  - 136: {fileID: 13653526}
  - 23: {fileID: 2366366}
  - 195: {fileID: 19546718}
  - 114: {fileID: 11445994}
  - 114: {fileID: 11411056}
  m_Layer: 11
  m_Name: Bot 5
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &494800
Transform:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 100100000}
  m_GameObject: {fileID: 171016}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 125.900009, y: -6.0999999, z: 45.5999985}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 0}
  m_RootOrder: 0
--- !u!23 &2366366
MeshRenderer:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 100100000}
  m_GameObject: {fileID: 171016}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_Materials:
  - {fileID: 2100000, guid: ac9383865ea2661438a716396a929efc, type: 2}
  m_SubsetIndices: 
  m_StaticBatchRoot: {fileID: 0}
  m_UseLightProbes: 1
  m_ReflectionProbeUsage: 1
  m_ProbeAnchor: {fileID: 0}
  m_ScaleInLightmap: 1
  m_PreserveUVs: 1
  m_ImportantGI: 0
  m_AutoUVMaxDistance: .5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingOrder: 0
--- !u!33 &3354176
MeshFilter:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 100100000}
  m_GameObject: {fileID: 171016}
  m_Mesh: {fileID: 10206, guid: 0000000000000000e000000000000000, type: 0}
--- !u!114 &11411056
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 100100000}
  m_GameObject: {fileID: 171016}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f131dd35d296fca4e90efc4ed717e48b, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  GraphicPrefab: {fileID: 197840, guid: 2b4fb31f6472842468de0a7249d7b8da, type: 2}
  Target: {fileID: 0}
  OffSet: {x: 0, y: 0, z: 0}
  Icon: {fileID: 21300086, guid: b2ff3b281537fbc4d91af3e848afde5c, type: 3}
  DeathIcon: {fileID: 21300018, guid: b2ff3b281537fbc4d91af3e848afde5c, type: 3}
  IconColor: {r: .0656898841, g: .992647052, b: .600681782, a: 1}
  Size: 14
  ShowCircleArea: 0
  CircleAreaRadius: 10
  CircleAreaColor: {r: 1, g: 1, b: 1, a: .899999976}
  isInteractable: 1
  InfoItem: This is a Bot too!
  OffScreen: 1
  BorderOffScreen: 0
  OffScreenSize: 9
  RenderDelay: .5
  m_Effect: 2
--- !u!114 &11445994
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 100100000}
  m_GameObject: {fileID: 171016}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d8614a12ab2784c4f8aa2eb5a541b6f4, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  Radius: 150
--- !u!136 &13653526
CapsuleCollider:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 100100000}
  m_GameObject: {fileID: 171016}
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_Enabled: 1
  m_Radius: .5
  m_Height: 2
  m_Direction: 1
  m_Center: {x: 0, y: 0, z: 0}
--- !u!195 &19546718
NavMeshAgent:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 100100000}
  m_GameObject: {fileID: 171016}
  m_Enabled: 1
  m_Radius: .500000119
  m_Speed: 10
  m_Acceleration: 8
  avoidancePriority: 50
  m_AngularSpeed: 120
  m_StoppingDistance: 0
  m_AutoTraverseOffMeshLink: 1
  m_AutoBraking: 1
  m_AutoRepath: 1
  m_Height: 2
  m_BaseOffset: 1
  m_WalkableMask: 4294967295
  m_ObstacleAvoidanceType: 4
--- !u!1001 &100100000
Prefab:
  m_ObjectHideFlags: 1
  serializedVersion: 2
  m_Modification:
    m_TransformParent: {fileID: 0}
    m_Modifications: []
    m_RemovedComponents: []
  m_ParentPrefab: {fileID: 0}
  m_RootGameObject: {fileID: 171016}
  m_IsPrefabParent: 1
