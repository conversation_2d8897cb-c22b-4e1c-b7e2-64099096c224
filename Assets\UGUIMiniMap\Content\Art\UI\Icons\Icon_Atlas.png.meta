fileFormatVersion: 2
guid: b2ff3b281537fbc4d91af3e848afde5c
TextureImporter:
  internalIDToNameTable:
  - first:
      213: 21300000
    second: Clock
  - first:
      213: 21300002
    second: Check
  - first:
      213: 21300004
    second: Audio
  - first:
      213: 21300006
    second: Icon_Atlas_3
  - first:
      213: 21300008
    second: Arrow_Left
  - first:
      213: 21300010
    second: Icon_Atlas_5
  - first:
      213: 21300012
    second: Refresh
  - first:
      213: 21300014
    second: Error
  - first:
      213: 21300016
    second: Icon_Atlas_8
  - first:
      213: 21300018
    second: Cruz
  - first:
      213: 21300020
    second: MoonAndStar
  - first:
      213: 21300022
    second: Triangle
  - first:
      213: 21300024
    second: Cruz2
  - first:
      213: 21300026
    second: Area_Check
  - first:
      213: 21300028
    second: Heart
  - first:
      213: 21300030
    second: Icon_Atlas_15
  - first:
      213: 21300032
    second: Cuadro
  - first:
      213: 21300034
    second: Icon_Atlas_17
  - first:
      213: 21300036
    second: Icon_Atlas_18
  - first:
      213: 21300038
    second: Icon_Atlas_19
  - first:
      213: 21300040
    second: Icon_Atlas_20
  - first:
      213: 21300042
    second: Icon_Atlas_21
  - first:
      213: 21300044
    second: Icon_Atlas_22
  - first:
      213: 21300046
    second: Icon_Atlas_23
  - first:
      213: 21300048
    second: Arrow_Player
  - first:
      213: 21300050
    second: Icon_Atlas_25
  - first:
      213: 21300052
    second: Pause_Circle
  - first:
      213: 21300054
    second: Icon_Atlas_27
  - first:
      213: 21300056
    second: Audio_Middle
  - first:
      213: 21300058
    second: Icon_Atlas_29
  - first:
      213: 21300060
    second: Circle_Health
  - first:
      213: 21300062
    second: Money_Circle
  - first:
      213: 21300064
    second: Indicator_2
  - first:
      213: 21300066
    second: Enter_Area2
  - first:
      213: 21300068
    second: Icon_Atlas_34
  - first:
      213: 21300070
    second: Enter_Area
  - first:
      213: 21300072
    second: Icon_Atlas_36
  - first:
      213: 21300074
    second: Indicator
  - first:
      213: 21300076
    second: Icon_Atlas_38
  - first:
      213: 21300078
    second: House
  - first:
      213: 21300080
    second: Icon_Atlas_40
  - first:
      213: 21300082
    second: Bell
  - first:
      213: 21300084
    second: Focus
  - first:
      213: 21300086
    second: Warning_Circle
  - first:
      213: 21300088
    second: Point_Target
  - first:
      213: 21300090
    second: Icon_Atlas_45
  - first:
      213: 21300092
    second: Tag
  - first:
      213: 21300094
    second: Icon_Atlas_47
  - first:
      213: 21300096
    second: Icon_Atlas_48
  - first:
      213: 21300098
    second: Icon_Atlas_49
  - first:
      213: 21300100
    second: Icon_Atlas_50
  - first:
      213: 21300102
    second: Icon_Atlas_51
  - first:
      213: 21300104
    second: Icon_Atlas_52
  - first:
      213: 21300106
    second: Start_6
  - first:
      213: 21300108
    second: Start
  - first:
      213: 21300110
    second: Equiz
  - first:
      213: 21300112
    second: Circle3
  - first:
      213: 21300114
    second: Circle
  - first:
      213: 21300116
    second: Warning_Area
  - first:
      213: 21300118
    second: Rockt
  - first:
      213: 21300120
    second: Icon_Atlas_60
  - first:
      213: 21300122
    second: Icon_Atlas_61
  - first:
      213: 21300124
    second: Icon_Atlas_62
  - first:
      213: 21300126
    second: Icon_Atlas_63
  - first:
      213: 21300128
    second: Icon_Atlas_64
  - first:
      213: 21300130
    second: Icon_Atlas_65
  - first:
      213: 21300132
    second: Icon_Atlas_66
  - first:
      213: 21300134
    second: Phone
  externalObjects: {}
  serializedVersion: 13
  mipmaps:
    mipMapMode: 0
    enableMipMap: 0
    sRGBTexture: 1
    linearTexture: 0
    fadeOut: 0
    borderMipMap: 0
    mipMapsPreserveCoverage: 0
    alphaTestReferenceValue: 0.5
    mipMapFadeDistanceStart: 1
    mipMapFadeDistanceEnd: 3
  bumpmap:
    convertToNormalMap: 0
    externalNormalMap: 0
    heightScale: 0.25
    normalMapFilter: 0
    flipGreenChannel: 0
  isReadable: 0
  streamingMipmaps: 0
  streamingMipmapsPriority: 0
  vTOnly: 0
  ignoreMipmapLimit: 0
  grayScaleToAlpha: 0
  generateCubemap: 6
  cubemapConvolution: 0
  seamlessCubemap: 0
  textureFormat: -3
  maxTextureSize: 1024
  textureSettings:
    serializedVersion: 2
    filterMode: 1
    aniso: 16
    mipBias: 0
    wrapU: 1
    wrapV: 1
    wrapW: 1
  nPOTScale: 0
  lightmap: 0
  compressionQuality: 50
  spriteMode: 2
  spriteExtrude: 1
  spriteMeshType: 1
  alignment: 0
  spritePivot: {x: 0.5, y: 0.5}
  spritePixelsToUnits: 100
  spriteBorder: {x: 0, y: 0, z: 0, w: 0}
  spriteGenerateFallbackPhysicsShape: 1
  alphaUsage: 1
  alphaIsTransparency: 1
  spriteTessellationDetail: -1
  textureType: 8
  textureShape: 1
  singleChannelComponent: 0
  flipbookRows: 1
  flipbookColumns: 1
  maxTextureSizeSet: 0
  compressionQualitySet: 0
  textureFormatSet: 0
  ignorePngGamma: 0
  applyGammaDecoding: 1
  swizzle: 50462976
  cookieLightType: 1
  platformSettings:
  - serializedVersion: 3
    buildTarget: DefaultTexturePlatform
    maxTextureSize: 1024
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 0
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 1
  - serializedVersion: 3
    buildTarget: Standalone
    maxTextureSize: 1024
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 0
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 1
  - serializedVersion: 3
    buildTarget: Android
    maxTextureSize: 1024
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 0
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 1
  - serializedVersion: 3
    buildTarget: Windows Store Apps
    maxTextureSize: 1024
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 0
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 1
  - serializedVersion: 3
    buildTarget: WebGL
    maxTextureSize: 1024
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 0
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 1
  spriteSheet:
    serializedVersion: 2
    sprites:
    - serializedVersion: 2
      name: Clock
      rect:
        serializedVersion: 2
        x: 2
        y: 900
        width: 61
        height: 63
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: a4f656b2f25aa794199cabada0043e0f
      internalID: 21300000
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Check
      rect:
        serializedVersion: 2
        x: 71
        y: 913
        width: 51
        height: 37
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 59addbc5ee0788146a68d48792f1562d
      internalID: 21300002
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Audio
      rect:
        serializedVersion: 2
        x: 133
        y: 912
        width: 58
        height: 39
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 1b842bb74e88d334a9006ec0fa4349ff
      internalID: 21300004
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Arrow_Left
      rect:
        serializedVersion: 2
        x: 200
        y: 905
        width: 51
        height: 53
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 0a9056313135082499c8c3489cb2b536
      internalID: 21300008
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Refresh
      rect:
        serializedVersion: 2
        x: 10
        y: 784
        width: 105
        height: 104
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: d584e6b4252455e4b918984c5a94cf22
      internalID: 21300012
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Error
      rect:
        serializedVersion: 2
        x: 136
        y: 779
        width: 113
        height: 112
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 263ae09f08561d9468cb8a96b31dfd0b
      internalID: 21300014
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Cruz
      rect:
        serializedVersion: 2
        x: 6
        y: 649
        width: 116
        height: 115
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 370988b00ef450b4aac35c09aef0804a
      internalID: 21300018
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: MoonAndStar
      rect:
        serializedVersion: 2
        x: 140
        y: 645
        width: 100
        height: 123
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: fe56e7dfc7d5c6c4e89909f80dc18378
      internalID: 21300020
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Triangle
      rect:
        serializedVersion: 2
        x: 2
        y: 524
        width: 124
        height: 107
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 8999fc9a36da86f4ab0db31a812b92a0
      internalID: 21300022
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Cruz2
      rect:
        serializedVersion: 2
        x: 145
        y: 519
        width: 95
        height: 118
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 4208e5865b6fcce468c32a5ee4a59a25
      internalID: 21300024
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Area_Check
      rect:
        serializedVersion: 2
        x: 4
        y: 393
        width: 113
        height: 112
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: fa8fef92e59490541b7df4b5e86421d3
      internalID: 21300026
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Heart
      rect:
        serializedVersion: 2
        x: 137
        y: 398
        width: 111
        height: 103
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: e110ee90eee7e2c458d23061f4de1d80
      internalID: 21300028
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Icon_Atlas_15
      rect:
        serializedVersion: 2
        x: 1
        y: 258
        width: 126
        height: 126
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 32ae0bdd76e93934598d802c4e9ea2d1
      internalID: 21300030
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Cuadro
      rect:
        serializedVersion: 2
        x: 138
        y: 266
        width: 109
        height: 110
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 43829f29ccb0022409f58e5ff95b1b3f
      internalID: 21300032
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Icon_Atlas_17
      rect:
        serializedVersion: 2
        x: 268
        y: 328
        width: 42
        height: 51
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 915d4affbdf60f94f9f60a962e17181d
      internalID: 21300034
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Icon_Atlas_18
      rect:
        serializedVersion: 2
        x: 326
        y: 333
        width: 30
        height: 41
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: f680c891bd8a785408a5549ebc577486
      internalID: 21300036
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Icon_Atlas_19
      rect:
        serializedVersion: 2
        x: 360
        y: 343
        width: 21
        height: 21
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 0c91bed410736614faf8bf5d0b75971c
      internalID: 21300038
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Icon_Atlas_20
      rect:
        serializedVersion: 2
        x: 391
        y: 334
        width: 54
        height: 39
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 2e3bbedce8c12c64997db0ac42d74e22
      internalID: 21300040
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Icon_Atlas_21
      rect:
        serializedVersion: 2
        x: 454
        y: 322
        width: 57
        height: 57
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: cd4ebba75d2ccce41b85367b749ec5e2
      internalID: 21300042
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Icon_Atlas_22
      rect:
        serializedVersion: 2
        x: 262
        y: 260
        width: 54
        height: 58
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: b6f61dd1c55291240aea465309740b88
      internalID: 21300044
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Icon_Atlas_23
      rect:
        serializedVersion: 2
        x: 325
        y: 261
        width: 57
        height: 56
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: fe85bc6d8247c0344b9edbbe80a7170d
      internalID: 21300046
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Arrow_Player
      rect:
        serializedVersion: 2
        x: 388
        y: 257
        width: 60
        height: 64
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 0938facc4a0e45f4ea9159e46febacde
      internalID: 21300048
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Icon_Atlas_25
      rect:
        serializedVersion: 2
        x: 517
        y: 273
        width: 58
        height: 26
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 0f96484dd5ea62444907decc392ae497
      internalID: 21300050
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Pause_Circle
      rect:
        serializedVersion: 2
        x: 584
        y: 262
        width: 54
        height: 53
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: a9ddc08a8d5de664c80201c5313a3238
      internalID: 21300052
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Audio_Middle
      rect:
        serializedVersion: 2
        x: 647
        y: 266
        width: 56
        height: 47
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 4c74ed69c3aed9d4e8e6178af755bab7
      internalID: 21300056
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Circle_Health
      rect:
        serializedVersion: 2
        x: 714
        y: 262
        width: 52
        height: 53
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: a405130ca0f784640b86711326b5b60a
      internalID: 21300060
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Money_Circle
      rect:
        serializedVersion: 2
        x: 777
        y: 264
        width: 53
        height: 51
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: b1b3ea7c4a8dba344928e77d60178a8e
      internalID: 21300062
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Indicator_2
      rect:
        serializedVersion: 2
        x: 849
        y: 261
        width: 39
        height: 57
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 63b0c8c4dcc5db74eb39b30e908dd93e
      internalID: 21300064
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Enter_Area2
      rect:
        serializedVersion: 2
        x: 909
        y: 259
        width: 48
        height: 60
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 28764cf46f1dce4428ac11eb4009eadb
      internalID: 21300066
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Enter_Area
      rect:
        serializedVersion: 2
        x: 11
        y: 135
        width: 114
        height: 113
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: b91b2cccfbd29454981677a017b43455
      internalID: 21300070
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Icon_Atlas_36
      rect:
        serializedVersion: 2
        x: 129
        y: 129
        width: 127
        height: 127
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 748455a717035de41a176ba5c218bc25
      internalID: 21300072
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Indicator
      rect:
        serializedVersion: 2
        x: 408
        y: 134
        width: 81
        height: 117
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: be77e0be2e102254b98780af3269c7e0
      internalID: 21300074
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Icon_Atlas_38
      rect:
        serializedVersion: 2
        x: 513
        y: 128
        width: 127
        height: 126
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: fd4fe01adb63a3249a917ac82704d5ba
      internalID: 21300076
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: House
      rect:
        serializedVersion: 2
        x: 642
        y: 199
        width: 59
        height: 52
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 27e6c19c8c16eac46a85c52dcf050aaf
      internalID: 21300078
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Icon_Atlas_40
      rect:
        serializedVersion: 2
        x: 726
        y: 215
        width: 20
        height: 20
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: da55faf5934926142a97320edbf872c4
      internalID: 21300080
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Bell
      rect:
        serializedVersion: 2
        x: 778
        y: 196
        width: 45
        height: 58
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: bcc3e1bf5ae4404419c5dd54c0a9e30d
      internalID: 21300082
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Focus
      rect:
        serializedVersion: 2
        x: 837
        y: 208
        width: 58
        height: 34
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: b8673a83203619f43a58d841a9f23951
      internalID: 21300084
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warning_Circle
      rect:
        serializedVersion: 2
        x: 904
        y: 200
        width: 52
        height: 52
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: cd78f5310b298ce40b9a1b42ece26227
      internalID: 21300086
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Point_Target
      rect:
        serializedVersion: 2
        x: 257
        y: 127
        width: 131
        height: 129
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 8e2e77824ead23c418140ed128e49249
      internalID: 21300088
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Tag
      rect:
        serializedVersion: 2
        x: 644
        y: 140
        width: 55
        height: 41
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 0f617e4a7722bbe49bb4f2d8d17ddfc9
      internalID: 21300092
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Icon_Atlas_47
      rect:
        serializedVersion: 2
        x: 707
        y: 137
        width: 58
        height: 47
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: dc6ae3353499ce84783ef3234b532cf3
      internalID: 21300094
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Icon_Atlas_48
      rect:
        serializedVersion: 2
        x: 772
        y: 132
        width: 57
        height: 58
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 9b53c9389176a1f4fa2aeb1f4d162028
      internalID: 21300096
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Icon_Atlas_49
      rect:
        serializedVersion: 2
        x: 844
        y: 135
        width: 42
        height: 51
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: c0aae847f3bcfc24b871462d914e777c
      internalID: 21300098
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Icon_Atlas_50
      rect:
        serializedVersion: 2
        x: 901
        y: 132
        width: 57
        height: 58
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: a9fc12d5acdd85a42b1aef6b9782d4d0
      internalID: 21300100
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Icon_Atlas_51
      rect:
        serializedVersion: 2
        x: 13
        y: 53
        width: 41
        height: 57
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 33d216acf1fd1634e8936906838ce4f3
      internalID: 21300102
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Icon_Atlas_52
      rect:
        serializedVersion: 2
        x: 66
        y: 56
        width: 49
        height: 61
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 913b9d1c55091d042a30422fd29c21da
      internalID: 21300104
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Start_6
      rect:
        serializedVersion: 2
        x: 141
        y: 5
        width: 103
        height: 118
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: dd85e15bcbcb6cf4eb94ae5c16651853
      internalID: 21300106
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Start
      rect:
        serializedVersion: 2
        x: 259
        y: 5
        width: 124
        height: 117
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 7ceb4569f8d2bc34b87092d2b1e2fa3e
      internalID: 21300108
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Equiz
      rect:
        serializedVersion: 2
        x: 394
        y: 9
        width: 111
        height: 110
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 6f7144e0c8bbe004e97df35c3f69da5e
      internalID: 21300110
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Circle3
      rect:
        serializedVersion: 2
        x: 518
        y: 4
        width: 120
        height: 120
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 32e9d374e5caa2347ac03b43614390b7
      internalID: 21300112
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Circle
      rect:
        serializedVersion: 2
        x: 648
        y: 5
        width: 117
        height: 118
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 35ceaa8f36d386d48a52a4eff08e0c14
      internalID: 21300114
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warning_Area
      rect:
        serializedVersion: 2
        x: 774
        y: 3
        width: 122
        height: 121
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 39a670d558874354c863867536eb4e30
      internalID: 21300116
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Rockt
      rect:
        serializedVersion: 2
        x: 911
        y: 63
        width: 41
        height: 65
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 9a93129bf5b1dc5498b7fa1b5fe0abca
      internalID: 21300118
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Icon_Atlas_65
      rect:
        serializedVersion: 2
        x: 30
        y: 11
        width: 66
        height: 38
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 9fcb925294a43594185a2554d5928bad
      internalID: 21300130
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Phone
      rect:
        serializedVersion: 2
        x: 915
        y: 4
        width: 33
        height: 57
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 3f2f30902f5b58d4f8b6be115a099957
      internalID: 21300134
      vertices: []
      indices: 
      edges: []
      weights: []
    outline: []
    physicsShape: []
    bones: []
    spriteID: 
    internalID: 0
    vertices: []
    indices: 
    edges: []
    weights: []
    secondaryTextures: []
    nameFileIdTable:
      Area_Check: 21300026
      Arrow_Left: 21300008
      Arrow_Player: 21300048
      Audio: 21300004
      Audio_Middle: 21300056
      Bell: 21300082
      Check: 21300002
      Circle: 21300114
      Circle3: 21300112
      Circle_Health: 21300060
      Clock: 21300000
      Cruz: 21300018
      Cruz2: 21300024
      Cuadro: 21300032
      Enter_Area: 21300070
      Enter_Area2: 21300066
      Equiz: 21300110
      Error: 21300014
      Focus: 21300084
      Heart: 21300028
      House: 21300078
      Icon_Atlas_15: 21300030
      Icon_Atlas_17: 21300034
      Icon_Atlas_18: 21300036
      Icon_Atlas_19: 21300038
      Icon_Atlas_20: 21300040
      Icon_Atlas_21: 21300042
      Icon_Atlas_22: 21300044
      Icon_Atlas_23: 21300046
      Icon_Atlas_25: 21300050
      Icon_Atlas_36: 21300072
      Icon_Atlas_38: 21300076
      Icon_Atlas_40: 21300080
      Icon_Atlas_47: 21300094
      Icon_Atlas_48: 21300096
      Icon_Atlas_49: 21300098
      Icon_Atlas_50: 21300100
      Icon_Atlas_51: 21300102
      Icon_Atlas_52: 21300104
      Icon_Atlas_65: 21300130
      Indicator: 21300074
      Indicator_2: 21300064
      Money_Circle: 21300062
      MoonAndStar: 21300020
      Pause_Circle: 21300052
      Phone: 21300134
      Point_Target: 21300088
      Refresh: 21300012
      Rockt: 21300118
      Start: 21300108
      Start_6: 21300106
      Tag: 21300092
      Triangle: 21300022
      Warning_Area: 21300116
      Warning_Circle: 21300086
  mipmapLimitGroupName: 
  pSDRemoveMatte: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
