%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!74 &7400000
AnimationClip:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_Name: BottonBar
  serializedVersion: 6
  m_Legacy: 0
  m_Compressed: 0
  m_UseHighQualityCurve: 1
  m_RotationCurves: []
  m_CompressedRotationCurves: []
  m_PositionCurves: []
  m_ScaleCurves: []
  m_FloatCurves:
  - curve:
      serializedVersion: 2
      m_Curve:
      - time: 0
        value: -69.9250031
        inSlope: 25.4833355
        outSlope: 25.4833355
        tangentMode: 10
      - time: 1.5
        value: -31.7000008
        inSlope: 25.4833355
        outSlope: 25.4833355
        tangentMode: 10
      m_PreInfinity: 2
      m_PostInfinity: 2
    attribute: m_AnchoredPosition.x
    path: 
    classID: 224
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve:
      - time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 10
      - time: 1.5
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 10
      m_PreInfinity: 2
      m_PostInfinity: 2
    attribute: m_AnchoredPosition.y
    path: 
    classID: 224
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve:
      - time: 0
        value: -139.850006
        inSlope: 50.966671
        outSlope: 50.966671
        tangentMode: 10
      - time: 1.5
        value: -63.4000015
        inSlope: 50.966671
        outSlope: 50.966671
        tangentMode: 10
      m_PreInfinity: 2
      m_PostInfinity: 2
    attribute: m_SizeDelta.x
    path: 
    classID: 224
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve:
      - time: 0
        value: 19.1000004
        inSlope: 0
        outSlope: 0
        tangentMode: 10
      - time: 1.5
        value: 19.1000004
        inSlope: 0
        outSlope: 0
        tangentMode: 10
      m_PreInfinity: 2
      m_PostInfinity: 2
    attribute: m_SizeDelta.y
    path: 
    classID: 224
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve:
      - time: 0
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 10
      - time: 1.5
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 10
      m_PreInfinity: 2
      m_PostInfinity: 2
    attribute: m_Alpha
    path: 
    classID: 225
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve:
      - time: 0
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 10
      - time: 1.5
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 10
      m_PreInfinity: 2
      m_PostInfinity: 2
    attribute: m_Color.r
    path: Botton/Text
    classID: 114
    script: {fileID: 708705254, guid: f5f67c52d1564df4a8936ccd202a3bd8, type: 3}
  - curve:
      serializedVersion: 2
      m_Curve:
      - time: 0
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 10
      - time: 1.5
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 10
      m_PreInfinity: 2
      m_PostInfinity: 2
    attribute: m_Color.g
    path: Botton/Text
    classID: 114
    script: {fileID: 708705254, guid: f5f67c52d1564df4a8936ccd202a3bd8, type: 3}
  - curve:
      serializedVersion: 2
      m_Curve:
      - time: 0
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 10
      - time: 1.5
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 10
      m_PreInfinity: 2
      m_PostInfinity: 2
    attribute: m_Color.b
    path: Botton/Text
    classID: 114
    script: {fileID: 708705254, guid: f5f67c52d1564df4a8936ccd202a3bd8, type: 3}
  - curve:
      serializedVersion: 2
      m_Curve:
      - time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 10
      - time: .666666687
        value: 0
        inSlope: .600000024
        outSlope: .600000024
        tangentMode: 10
      - time: 1.5
        value: 1
        inSlope: 1.20000005
        outSlope: 1.20000005
        tangentMode: 10
      m_PreInfinity: 2
      m_PostInfinity: 2
    attribute: m_Color.a
    path: Botton/Text
    classID: 114
    script: {fileID: 708705254, guid: f5f67c52d1564df4a8936ccd202a3bd8, type: 3}
  m_PPtrCurves: []
  m_SampleRate: 60
  m_WrapMode: 0
  m_Bounds:
    m_Center: {x: 0, y: 0, z: 0}
    m_Extent: {x: 0, y: 0, z: 0}
  m_ClipBindingConstant:
    genericBindings:
    - path: 0
      attribute: 1460864421
      script: {fileID: 0}
      classID: 224
      customType: 0
      isPPtrCurve: 0
    - path: 0
      attribute: 1967290853
      script: {fileID: 0}
      classID: 224
      customType: 0
      isPPtrCurve: 0
    - path: 4134960794
      attribute: 304273561
      script: {fileID: 708705254, guid: f5f67c52d1564df4a8936ccd202a3bd8, type: 3}
      classID: 114
      customType: 0
      isPPtrCurve: 0
    - path: 0
      attribute: 538195251
      script: {fileID: 0}
      classID: 224
      customType: 0
      isPPtrCurve: 0
    - path: 0
      attribute: 38095219
      script: {fileID: 0}
      classID: 224
      customType: 0
      isPPtrCurve: 0
    - path: 0
      attribute: 1574349066
      script: {fileID: 0}
      classID: 225
      customType: 0
      isPPtrCurve: 0
    - path: 4134960794
      attribute: 2526845255
      script: {fileID: 708705254, guid: f5f67c52d1564df4a8936ccd202a3bd8, type: 3}
      classID: 114
      customType: 0
      isPPtrCurve: 0
    - path: 4134960794
      attribute: 4215373228
      script: {fileID: 708705254, guid: f5f67c52d1564df4a8936ccd202a3bd8, type: 3}
      classID: 114
      customType: 0
      isPPtrCurve: 0
    - path: 4134960794
      attribute: 2334886179
      script: {fileID: 708705254, guid: f5f67c52d1564df4a8936ccd202a3bd8, type: 3}
      classID: 114
      customType: 0
      isPPtrCurve: 0
    pptrCurveMapping: []
  m_AnimationClipSettings:
    serializedVersion: 2
    m_StartTime: 0
    m_StopTime: 1.5
    m_OrientationOffsetY: 0
    m_Level: 0
    m_CycleOffset: 0
    m_LoopTime: 0
    m_LoopBlend: 0
    m_LoopBlendOrientation: 0
    m_LoopBlendPositionY: 0
    m_LoopBlendPositionXZ: 0
    m_KeepOriginalOrientation: 0
    m_KeepOriginalPositionY: 1
    m_KeepOriginalPositionXZ: 0
    m_HeightFromFeet: 0
    m_Mirror: 0
  m_EditorCurves:
  - curve:
      serializedVersion: 2
      m_Curve:
      - time: 0
        value: -69.9250031
        inSlope: 25.4833355
        outSlope: 25.4833355
        tangentMode: 10
      - time: 1.5
        value: -31.7000008
        inSlope: 25.4833355
        outSlope: 25.4833355
        tangentMode: 10
      m_PreInfinity: 2
      m_PostInfinity: 2
    attribute: m_AnchoredPosition.x
    path: 
    classID: 224
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve:
      - time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 10
      - time: 1.5
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 10
      m_PreInfinity: 2
      m_PostInfinity: 2
    attribute: m_AnchoredPosition.y
    path: 
    classID: 224
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve:
      - time: 0
        value: -139.850006
        inSlope: 50.966671
        outSlope: 50.966671
        tangentMode: 10
      - time: 1.5
        value: -63.4000015
        inSlope: 50.966671
        outSlope: 50.966671
        tangentMode: 10
      m_PreInfinity: 2
      m_PostInfinity: 2
    attribute: m_SizeDelta.x
    path: 
    classID: 224
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve:
      - time: 0
        value: 19.1000004
        inSlope: 0
        outSlope: 0
        tangentMode: 10
      - time: 1.5
        value: 19.1000004
        inSlope: 0
        outSlope: 0
        tangentMode: 10
      m_PreInfinity: 2
      m_PostInfinity: 2
    attribute: m_SizeDelta.y
    path: 
    classID: 224
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve:
      - time: 0
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 10
      - time: 1.5
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 10
      m_PreInfinity: 2
      m_PostInfinity: 2
    attribute: m_Alpha
    path: 
    classID: 225
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve:
      - time: 0
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 10
      - time: 1.5
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 10
      m_PreInfinity: 2
      m_PostInfinity: 2
    attribute: m_Color.r
    path: Botton/Text
    classID: 114
    script: {fileID: 708705254, guid: f5f67c52d1564df4a8936ccd202a3bd8, type: 3}
  - curve:
      serializedVersion: 2
      m_Curve:
      - time: 0
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 10
      - time: 1.5
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 10
      m_PreInfinity: 2
      m_PostInfinity: 2
    attribute: m_Color.g
    path: Botton/Text
    classID: 114
    script: {fileID: 708705254, guid: f5f67c52d1564df4a8936ccd202a3bd8, type: 3}
  - curve:
      serializedVersion: 2
      m_Curve:
      - time: 0
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 10
      - time: 1.5
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 10
      m_PreInfinity: 2
      m_PostInfinity: 2
    attribute: m_Color.b
    path: Botton/Text
    classID: 114
    script: {fileID: 708705254, guid: f5f67c52d1564df4a8936ccd202a3bd8, type: 3}
  - curve:
      serializedVersion: 2
      m_Curve:
      - time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 10
      - time: .666666687
        value: 0
        inSlope: .600000024
        outSlope: .600000024
        tangentMode: 10
      - time: 1.5
        value: 1
        inSlope: 1.20000005
        outSlope: 1.20000005
        tangentMode: 10
      m_PreInfinity: 2
      m_PostInfinity: 2
    attribute: m_Color.a
    path: Botton/Text
    classID: 114
    script: {fileID: 708705254, guid: f5f67c52d1564df4a8936ccd202a3bd8, type: 3}
  m_EulerEditorCurves: []
  m_HasGenericRootTransform: 0
  m_HasMotionFloatCurves: 0
  m_GenerateMotionCurves: 0
  m_Events: []
