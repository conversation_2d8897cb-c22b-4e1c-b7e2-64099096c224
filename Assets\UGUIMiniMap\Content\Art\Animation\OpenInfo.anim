%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!74 &7400000
AnimationClip:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_Name: OpenInfo
  serializedVersion: 6
  m_Legacy: 0
  m_Compressed: 0
  m_UseHighQualityCurve: 1
  m_RotationCurves: []
  m_CompressedRotationCurves: []
  m_PositionCurves: []
  m_ScaleCurves: []
  m_FloatCurves:
  - curve:
      serializedVersion: 2
      m_Curve:
      - time: 0
        value: -14.75
        inSlope: 219.999985
        outSlope: 219.999985
        tangentMode: 10
      - time: .25
        value: 40.2499962
        inSlope: 109.70002
        outSlope: 109.70002
        tangentMode: 10
      - time: .333333343
        value: 40.2000008
        inSlope: -.599945009
        outSlope: -.599945009
        tangentMode: 10
      m_PreInfinity: 2
      m_PostInfinity: 2
    attribute: m_SizeDelta.x
    path: Text
    classID: 224
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve:
      - time: 0
        value: 0
        inSlope: 33.4799995
        outSlope: 33.4799995
        tangentMode: 10
      - time: .25
        value: 8.36999989
        inSlope: 108.119987
        outSlope: 108.119987
        tangentMode: 10
      - time: .333333343
        value: 23.6000004
        inSlope: 182.759979
        outSlope: 182.759979
        tangentMode: 10
      m_PreInfinity: 2
      m_PostInfinity: 2
    attribute: m_SizeDelta.y
    path: Text
    classID: 224
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve:
      - time: 0
        value: 7.375
        inSlope: 110
        outSlope: 110
        tangentMode: 10
      - time: .25
        value: 34.875
        inSlope: 55.1500092
        outSlope: 55.1500092
        tangentMode: 10
      - time: .333333343
        value: 34.9000015
        inSlope: .300018281
        outSlope: .300018281
        tangentMode: 10
      m_PreInfinity: 2
      m_PostInfinity: 2
    attribute: m_AnchoredPosition.x
    path: Text
    classID: 224
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve:
      - time: 0
        value: 0
        inSlope: -16.7399998
        outSlope: -16.7399998
        tangentMode: 10
      - time: .25
        value: -4.18499994
        inSlope: -54.0599937
        outSlope: -54.0599937
        tangentMode: 10
      - time: .333333343
        value: -11.8000002
        inSlope: -91.3799896
        outSlope: -91.3799896
        tangentMode: 10
      m_PreInfinity: 2
      m_PostInfinity: 2
    attribute: m_AnchoredPosition.y
    path: Text
    classID: 224
    script: {fileID: 0}
  m_PPtrCurves: []
  m_SampleRate: 60
  m_WrapMode: 0
  m_Bounds:
    m_Center: {x: 0, y: 0, z: 0}
    m_Extent: {x: 0, y: 0, z: 0}
  m_ClipBindingConstant:
    genericBindings:
    - path: 2612594937
      attribute: 1967290853
      script: {fileID: 0}
      classID: 224
      customType: 0
      isPPtrCurve: 0
    - path: 2612594937
      attribute: 38095219
      script: {fileID: 0}
      classID: 224
      customType: 0
      isPPtrCurve: 0
    - path: 2612594937
      attribute: 1460864421
      script: {fileID: 0}
      classID: 224
      customType: 0
      isPPtrCurve: 0
    - path: 2612594937
      attribute: 538195251
      script: {fileID: 0}
      classID: 224
      customType: 0
      isPPtrCurve: 0
    pptrCurveMapping: []
  m_AnimationClipSettings:
    serializedVersion: 2
    m_StartTime: 0
    m_StopTime: .333333343
    m_OrientationOffsetY: 0
    m_Level: 0
    m_CycleOffset: 0
    m_LoopTime: 1
    m_LoopBlend: 0
    m_LoopBlendOrientation: 0
    m_LoopBlendPositionY: 0
    m_LoopBlendPositionXZ: 0
    m_KeepOriginalOrientation: 0
    m_KeepOriginalPositionY: 1
    m_KeepOriginalPositionXZ: 0
    m_HeightFromFeet: 0
    m_Mirror: 0
  m_EditorCurves:
  - curve:
      serializedVersion: 2
      m_Curve:
      - time: 0
        value: -14.75
        inSlope: 219.999985
        outSlope: 219.999985
        tangentMode: 10
      - time: .25
        value: 40.2499962
        inSlope: 109.70002
        outSlope: 109.70002
        tangentMode: 10
      - time: .333333343
        value: 40.2000008
        inSlope: -.599945009
        outSlope: -.599945009
        tangentMode: 10
      m_PreInfinity: 2
      m_PostInfinity: 2
    attribute: m_SizeDelta.x
    path: Text
    classID: 224
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve:
      - time: 0
        value: 0
        inSlope: 33.4799995
        outSlope: 33.4799995
        tangentMode: 10
      - time: .25
        value: 8.36999989
        inSlope: 108.119987
        outSlope: 108.119987
        tangentMode: 10
      - time: .333333343
        value: 23.6000004
        inSlope: 182.759979
        outSlope: 182.759979
        tangentMode: 10
      m_PreInfinity: 2
      m_PostInfinity: 2
    attribute: m_SizeDelta.y
    path: Text
    classID: 224
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve:
      - time: 0
        value: 7.375
        inSlope: 110
        outSlope: 110
        tangentMode: 10
      - time: .25
        value: 34.875
        inSlope: 55.1500092
        outSlope: 55.1500092
        tangentMode: 10
      - time: .333333343
        value: 34.9000015
        inSlope: .300018281
        outSlope: .300018281
        tangentMode: 10
      m_PreInfinity: 2
      m_PostInfinity: 2
    attribute: m_AnchoredPosition.x
    path: Text
    classID: 224
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve:
      - time: 0
        value: 0
        inSlope: -16.7399998
        outSlope: -16.7399998
        tangentMode: 10
      - time: .25
        value: -4.18499994
        inSlope: -54.0599937
        outSlope: -54.0599937
        tangentMode: 10
      - time: .333333343
        value: -11.8000002
        inSlope: -91.3799896
        outSlope: -91.3799896
        tangentMode: 10
      m_PreInfinity: 2
      m_PostInfinity: 2
    attribute: m_AnchoredPosition.y
    path: Text
    classID: 224
    script: {fileID: 0}
  m_EulerEditorCurves: []
  m_HasGenericRootTransform: 0
  m_HasMotionFloatCurves: 0
  m_GenerateMotionCurves: 0
  m_Events: []
