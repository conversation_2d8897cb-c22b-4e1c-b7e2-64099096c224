%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!74 &7400000
AnimationClip:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_Name: Pulsing
  serializedVersion: 6
  m_Legacy: 0
  m_Compressed: 0
  m_UseHighQualityCurve: 1
  m_RotationCurves: []
  m_CompressedRotationCurves: []
  m_PositionCurves: []
  m_ScaleCurves:
  - curve:
      serializedVersion: 2
      m_Curve:
      - time: 0
        value: {x: 1, y: 1, z: 1}
        inSlope: {x: -.25, y: -.25, z: -.25}
        outSlope: {x: -.25, y: -.25, z: -.25}
        tangentMode: 0
      - time: 1
        value: {x: .75, y: .75, z: .75}
        inSlope: {x: 0, y: 0, z: 0}
        outSlope: {x: 0, y: 0, z: 0}
        tangentMode: 0
      - time: 2
        value: {x: 1, y: 1, z: 1}
        inSlope: {x: .25, y: .25, z: .25}
        outSlope: {x: .25, y: .25, z: .25}
        tangentMode: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
    path: 
  m_FloatCurves: []
  m_PPtrCurves: []
  m_SampleRate: 60
  m_WrapMode: 0
  m_Bounds:
    m_Center: {x: 0, y: 0, z: 0}
    m_Extent: {x: 0, y: 0, z: 0}
  m_ClipBindingConstant:
    genericBindings:
    - path: 0
      attribute: 3
      script: {fileID: 0}
      classID: 4
      customType: 0
      isPPtrCurve: 0
    pptrCurveMapping: []
  m_AnimationClipSettings:
    serializedVersion: 2
    m_StartTime: 0
    m_StopTime: 2
    m_OrientationOffsetY: 0
    m_Level: 0
    m_CycleOffset: 0
    m_LoopTime: 1
    m_LoopBlend: 0
    m_LoopBlendOrientation: 0
    m_LoopBlendPositionY: 0
    m_LoopBlendPositionXZ: 0
    m_KeepOriginalOrientation: 0
    m_KeepOriginalPositionY: 1
    m_KeepOriginalPositionXZ: 0
    m_HeightFromFeet: 0
    m_Mirror: 0
  m_EditorCurves:
  - curve:
      serializedVersion: 2
      m_Curve:
      - time: 0
        value: 1
        inSlope: -.25
        outSlope: -.25
        tangentMode: 10
      - time: 1
        value: .75
        inSlope: 0
        outSlope: 0
        tangentMode: 10
      - time: 2
        value: 1
        inSlope: .25
        outSlope: .25
        tangentMode: 10
      m_PreInfinity: 2
      m_PostInfinity: 2
    attribute: m_LocalScale.x
    path: 
    classID: 224
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve:
      - time: 0
        value: 1
        inSlope: -.25
        outSlope: -.25
        tangentMode: 10
      - time: 1
        value: .75
        inSlope: 0
        outSlope: 0
        tangentMode: 10
      - time: 2
        value: 1
        inSlope: .25
        outSlope: .25
        tangentMode: 10
      m_PreInfinity: 2
      m_PostInfinity: 2
    attribute: m_LocalScale.y
    path: 
    classID: 224
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve:
      - time: 0
        value: 1
        inSlope: -.25
        outSlope: -.25
        tangentMode: 10
      - time: 1
        value: .75
        inSlope: 0
        outSlope: 0
        tangentMode: 10
      - time: 2
        value: 1
        inSlope: .25
        outSlope: .25
        tangentMode: 10
      m_PreInfinity: 2
      m_PostInfinity: 2
    attribute: m_LocalScale.z
    path: 
    classID: 224
    script: {fileID: 0}
  m_EulerEditorCurves: []
  m_HasGenericRootTransform: 0
  m_HasMotionFloatCurves: 0
  m_GenerateMotionCurves: 0
  m_Events: []
