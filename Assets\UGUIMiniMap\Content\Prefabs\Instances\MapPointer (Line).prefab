%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!1 &6752995047064541712
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6754478050658086192}
  - component: {fileID: 6725516955564046430}
  - component: {fileID: 6639444321613090990}
  - component: {fileID: 6640494586627249262}
  - component: {fileID: 6671082434499343640}
  m_Layer: 1
  m_Name: MapPointer (Line)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &6754478050658086192
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6752995047064541712}
  m_LocalRotation: {x: 0, y: -0.9827099, z: 0, w: 0.18515207}
  m_LocalPosition: {x: -91.85739, y: -5.2000017, z: 4.5190973}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 2730616114353132652}
  m_Father: {fileID: 0}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: -158.66, z: 0}
--- !u!65 &6725516955564046430
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6752995047064541712}
  m_Material: {fileID: 0}
  m_IsTrigger: 1
  m_Enabled: 1
  serializedVersion: 2
  m_Size: {x: 8, y: 50, z: 8}
  m_Center: {x: -0.35780144, y: 23.242058, z: -0.5974703}
--- !u!114 &6639444321613090990
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6752995047064541712}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d2314256d221f424e995a8a82ca0d16e, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  PlayerTag: Player
  SpawnSound: {fileID: 8300000, guid: b5c2479289af1ce42b18f93453bc069c, type: 3}
  m_Render: {fileID: 0}
--- !u!114 &6640494586627249262
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6752995047064541712}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f131dd35d296fca4e90efc4ed717e48b, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  Target: {fileID: 6754478050658086192}
  OffSet: {x: 0, y: 0, z: 0}
  iconData: {fileID: 11400000, guid: dc947efe114c5064b8009ff883324f73, type: 2}
  ShowCircleArea: 0
  CircleAreaRadius: 10
  CircleAreaColor: {r: 1, g: 1, b: 1, a: 0.9}
  isInteractable: 1
  interacableAction: 1
  InfoItem: Info Icon here
  keepTextAlwaysFacingUp: 0
  onClick:
    m_PersistentCalls:
      m_Calls:
      - m_Target: {fileID: 6639444321613090990}
        m_TargetAssemblyTypeName: Lovatto.MiniMap.bl_MapPointer, Assembly-CSharp
        m_MethodName: Destroy
        m_Mode: 1
        m_Arguments:
          m_ObjectArgument: {fileID: 0}
          m_ObjectArgumentAssemblyTypeName: UnityEngine.Object, UnityEngine
          m_IntArgument: 0
          m_FloatArgument: 0
          m_StringArgument: 
          m_BoolArgument: 0
        m_CallState: 2
  OffScreen: 1
  opacityBasedDistance: 0
  maxDistance: 250
  iconFaceDirection: 1
  DestroyWithObject: 1
  BorderOffScreen: 0.01
  RenderDelay: 0
  m_Effect: 0
  CustomIconPrefab: {fileID: 0}
--- !u!82 &6671082434499343640
AudioSource:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6752995047064541712}
  m_Enabled: 1
  serializedVersion: 4
  OutputAudioMixerGroup: {fileID: 0}
  m_audioClip: {fileID: 0}
  m_PlayOnAwake: 0
  m_Volume: 1
  m_Pitch: 1
  Loop: 0
  Mute: 0
  Spatialize: 0
  SpatializePostEffects: 0
  Priority: 128
  DopplerLevel: 1
  MinDistance: 1
  MaxDistance: 500
  Pan2D: 0
  rolloffMode: 0
  BypassEffects: 0
  BypassListenerEffects: 0
  BypassReverbZones: 0
  rolloffCustomCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  panLevelCustomCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0.041
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 0
  spreadCustomCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  reverbZoneMixCustomCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 0
--- !u!1 &7898714845333705735
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2730616114353132652}
  - component: {fileID: 8799202068540547201}
  m_Layer: 1
  m_Name: Line
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2730616114353132652
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7898714845333705735}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 6754478050658086192}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!120 &8799202068540547201
LineRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7898714845333705735}
  m_Enabled: 1
  m_CastShadows: 0
  m_ReceiveShadows: 0
  m_DynamicOccludee: 1
  m_MotionVectors: 0
  m_LightProbeUsage: 0
  m_ReflectionProbeUsage: 0
  m_RayTracingMode: 0
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: b822b2d00a8ce1248b607679abbea34f, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_Positions:
  - {x: 0, y: 0, z: 0}
  - {x: 0, y: 40, z: 0}
  m_Parameters:
    serializedVersion: 3
    widthMultiplier: 2
    widthCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0.09581158
        outSlope: 0.09581158
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.80178064
      - serializedVersion: 3
        time: 0.31458247
        value: 0.05623941
        inSlope: 0.6680341
        outSlope: 0.6680341
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.38732305
        outWeight: 0.33333334
      - serializedVersion: 3
        time: 1
        value: 0.9937115
        inSlope: 1.4474456
        outSlope: 1.4474456
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.17116348
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    colorGradient:
      serializedVersion: 2
      key0: {r: 1, g: 0.8640462, b: 0, a: 0}
      key1: {r: 1, g: 0.9759526, b: 0.6933962, a: 1}
      key2: {r: 0, g: 0, b: 0, a: 1}
      key3: {r: 0, g: 0, b: 0, a: 0}
      key4: {r: 0, g: 0, b: 0, a: 0}
      key5: {r: 0, g: 0, b: 0, a: 0}
      key6: {r: 0, g: 0, b: 0, a: 0}
      key7: {r: 0, g: 0, b: 0, a: 0}
      ctime0: 0
      ctime1: 65535
      ctime2: 0
      ctime3: 0
      ctime4: 0
      ctime5: 0
      ctime6: 0
      ctime7: 0
      atime0: 0
      atime1: 8674
      atime2: 65535
      atime3: 0
      atime4: 0
      atime5: 0
      atime6: 0
      atime7: 0
      m_Mode: 0
      m_NumColorKeys: 2
      m_NumAlphaKeys: 3
    numCornerVertices: 0
    numCapVertices: 8
    alignment: 0
    textureMode: 0
    shadowBias: 0.5
    generateLightingData: 0
  m_UseWorldSpace: 0
  m_Loop: 0
