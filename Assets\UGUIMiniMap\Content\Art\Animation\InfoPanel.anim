%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!74 &7400000
AnimationClip:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_Name: InfoPanel
  serializedVersion: 6
  m_Legacy: 0
  m_Compressed: 0
  m_UseHighQualityCurve: 1
  m_RotationCurves: []
  m_CompressedRotationCurves: []
  m_PositionCurves: []
  m_ScaleCurves:
  - curve:
      serializedVersion: 2
      m_Curve:
      - time: 0
        value: {x: 1, y: 1, z: 1}
        inSlope: {x: 0, y: 0, z: 0}
        outSlope: {x: 0, y: 0, z: 0}
        tangentMode: 0
      - time: 1
        value: {x: 1, y: 1, z: 1}
        inSlope: {x: 0, y: 0, z: 0}
        outSlope: {x: 0, y: 0, z: 0}
        tangentMode: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
    path: 
  m_FloatCurves:
  - curve:
      serializedVersion: 2
      m_Curve:
      - time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 10
      - time: 1
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 10
      m_PreInfinity: 2
      m_PostInfinity: 2
    attribute: m_AnchoredPosition.x
    path: 
    classID: 224
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve:
      - time: 0
        value: 9.60000038
        inSlope: 0
        outSlope: 0
        tangentMode: 10
      - time: 1
        value: 9.60000038
        inSlope: 0
        outSlope: 0
        tangentMode: 10
      m_PreInfinity: 2
      m_PostInfinity: 2
    attribute: m_AnchoredPosition.y
    path: 
    classID: 224
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve:
      - time: 0
        value: 1
        inSlope: 71.3000031
        outSlope: 71.3000031
        tangentMode: 10
      - time: 1
        value: 72.3000031
        inSlope: 71.3000031
        outSlope: 71.3000031
        tangentMode: 10
      m_PreInfinity: 2
      m_PostInfinity: 2
    attribute: m_SizeDelta.x
    path: 
    classID: 224
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve:
      - time: 0
        value: -19.1000004
        inSlope: 0
        outSlope: 0
        tangentMode: 10
      - time: 1
        value: -19.1000004
        inSlope: 0
        outSlope: 0
        tangentMode: 10
      m_PreInfinity: 2
      m_PostInfinity: 2
    attribute: m_SizeDelta.y
    path: 
    classID: 224
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve:
      - time: 0
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 10
      - time: 1
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 10
      m_PreInfinity: 2
      m_PostInfinity: 2
    attribute: m_Alpha
    path: 
    classID: 225
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve:
      - time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 10
      - time: .583333313
        value: 0
        inSlope: 1.19999993
        outSlope: 1.19999993
        tangentMode: 10
      - time: 1
        value: 1
        inSlope: 2.39999986
        outSlope: 2.39999986
        tangentMode: 10
      m_PreInfinity: 2
      m_PostInfinity: 2
    attribute: m_Alpha
    path: Background/Content
    classID: 225
    script: {fileID: 0}
  m_PPtrCurves: []
  m_SampleRate: 60
  m_WrapMode: 0
  m_Bounds:
    m_Center: {x: 0, y: 0, z: 0}
    m_Extent: {x: 0, y: 0, z: 0}
  m_ClipBindingConstant:
    genericBindings:
    - path: 0
      attribute: 1967290853
      script: {fileID: 0}
      classID: 224
      customType: 0
      isPPtrCurve: 0
    - path: 78262719
      attribute: 1574349066
      script: {fileID: 0}
      classID: 225
      customType: 0
      isPPtrCurve: 0
    - path: 0
      attribute: 3
      script: {fileID: 0}
      classID: 4
      customType: 0
      isPPtrCurve: 0
    - path: 0
      attribute: 1460864421
      script: {fileID: 0}
      classID: 224
      customType: 0
      isPPtrCurve: 0
    - path: 0
      attribute: 538195251
      script: {fileID: 0}
      classID: 224
      customType: 0
      isPPtrCurve: 0
    - path: 0
      attribute: 38095219
      script: {fileID: 0}
      classID: 224
      customType: 0
      isPPtrCurve: 0
    - path: 0
      attribute: 1574349066
      script: {fileID: 0}
      classID: 225
      customType: 0
      isPPtrCurve: 0
    pptrCurveMapping: []
  m_AnimationClipSettings:
    serializedVersion: 2
    m_StartTime: 0
    m_StopTime: 1
    m_OrientationOffsetY: 0
    m_Level: 0
    m_CycleOffset: 0
    m_LoopTime: 0
    m_LoopBlend: 0
    m_LoopBlendOrientation: 0
    m_LoopBlendPositionY: 0
    m_LoopBlendPositionXZ: 0
    m_KeepOriginalOrientation: 0
    m_KeepOriginalPositionY: 1
    m_KeepOriginalPositionXZ: 0
    m_HeightFromFeet: 0
    m_Mirror: 0
  m_EditorCurves:
  - curve:
      serializedVersion: 2
      m_Curve:
      - time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 10
      - time: 1
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 10
      m_PreInfinity: 2
      m_PostInfinity: 2
    attribute: m_AnchoredPosition.x
    path: 
    classID: 224
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve:
      - time: 0
        value: 9.60000038
        inSlope: 0
        outSlope: 0
        tangentMode: 10
      - time: 1
        value: 9.60000038
        inSlope: 0
        outSlope: 0
        tangentMode: 10
      m_PreInfinity: 2
      m_PostInfinity: 2
    attribute: m_AnchoredPosition.y
    path: 
    classID: 224
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve:
      - time: 0
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 10
      - time: 1
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 10
      m_PreInfinity: 2
      m_PostInfinity: 2
    attribute: m_LocalScale.x
    path: 
    classID: 224
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve:
      - time: 0
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 10
      - time: 1
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 10
      m_PreInfinity: 2
      m_PostInfinity: 2
    attribute: m_LocalScale.y
    path: 
    classID: 224
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve:
      - time: 0
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 10
      - time: 1
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 10
      m_PreInfinity: 2
      m_PostInfinity: 2
    attribute: m_LocalScale.z
    path: 
    classID: 224
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve:
      - time: 0
        value: 1
        inSlope: 71.3000031
        outSlope: 71.3000031
        tangentMode: 10
      - time: 1
        value: 72.3000031
        inSlope: 71.3000031
        outSlope: 71.3000031
        tangentMode: 10
      m_PreInfinity: 2
      m_PostInfinity: 2
    attribute: m_SizeDelta.x
    path: 
    classID: 224
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve:
      - time: 0
        value: -19.1000004
        inSlope: 0
        outSlope: 0
        tangentMode: 10
      - time: 1
        value: -19.1000004
        inSlope: 0
        outSlope: 0
        tangentMode: 10
      m_PreInfinity: 2
      m_PostInfinity: 2
    attribute: m_SizeDelta.y
    path: 
    classID: 224
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve:
      - time: 0
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 10
      - time: 1
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 10
      m_PreInfinity: 2
      m_PostInfinity: 2
    attribute: m_Alpha
    path: 
    classID: 225
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve:
      - time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 10
      - time: .583333313
        value: 0
        inSlope: 1.19999993
        outSlope: 1.19999993
        tangentMode: 10
      - time: 1
        value: 1
        inSlope: 2.39999986
        outSlope: 2.39999986
        tangentMode: 10
      m_PreInfinity: 2
      m_PostInfinity: 2
    attribute: m_Alpha
    path: Background/Content
    classID: 225
    script: {fileID: 0}
  m_EulerEditorCurves: []
  m_HasGenericRootTransform: 0
  m_HasMotionFloatCurves: 0
  m_GenerateMotionCurves: 0
  m_Events: []
